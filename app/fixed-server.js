const express = require('express');
const axios = require('axios');
require('dotenv').config();

// Configuration
const config = {
  PORT: process.env.PORT || 3809,
  STEAM_API_KEY: process.env.STEAM_API_KEY,
  STEAM_APP_ID: process.env.STEAM_APP_ID,
  DEFAULT_TOP_RESULTS: 10,
  STEAM_API_BASE_URL: 'https://partner.steam-api.com',
  LEADERBOARDS_FOR_GAME_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardsForGame/v2/',
  LEADERBOARD_ENTRIES_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardEntries/v1/'
};

// Create Express app
const app = express();

// Middleware
app.use(express.json());

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'Steam Leaderboard API',
    endpoints: {
      leaderboards: '/tryu/leaderboard/:leaderboardKey',
      leaderboardsWithLimit: '/tryu/leaderboard/:leaderboardKey?limit=20'
    }
  });
});

// Leaderboard route
app.get('/tryu/leaderboard/:leaderboardKey', async (req, res) => {
  try {
    console.log(`Request received for leaderboard: ${req.params.leaderboardKey}`);
    
    // Get the leaderboard key from the URL
    const { leaderboardKey } = req.params;
    
    // Get the limit from query params or use default
    const limit = parseInt(req.query.limit) || config.DEFAULT_TOP_RESULTS;
    console.log(`Using limit: ${limit}`);
    
    // Get all leaderboards for the game
    console.log('Fetching leaderboards...');
    const leaderboardsResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARDS_FOR_GAME_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID
      }
    });
    
    // Format the leaderboards data
    const leaderboards = leaderboardsResponse.data.response.leaderboards || [];
    console.log(`Found ${leaderboards.length} leaderboards`);
    
    const formattedLeaderboards = {
      leaderBoards: {
        leaderBoardCount: leaderboards.length
      }
    };
    
    leaderboards.forEach(leaderboard => {
      formattedLeaderboards.leaderBoards[leaderboard.name] = {
        leaderBoardID: leaderboard.id,
        leaderBoardEntries: leaderboard.entries,
        leaderBoardSortMethod: leaderboard.sortmethod,
        leaderBoardDisplayType: leaderboard.displaytype,
        onlytrustedwrites: leaderboard.onlytrustedwrites || false,
        onlyfriendsreads: leaderboard.onlyfriendsreads || false,
        onlyusersinsameparty: leaderboard.onlyusersinsameparty || false,
        limitrangearounduser: leaderboard.limitrangearounduser || 0,
        limitglobaltopentries: leaderboard.limitglobaltopentries || 0
      };
    });
    
    // Check if the requested leaderboard exists
    if (!formattedLeaderboards.leaderBoards[leaderboardKey]) {
      console.log(`Leaderboard ${leaderboardKey} not found`);
      return res.status(404).json({ 
        error: 'Leaderboard not found',
        availableLeaderboards: Object.keys(formattedLeaderboards.leaderBoards)
          .filter(key => key !== 'leaderBoardCount')
      });
    }
    
    // Get the leaderboard ID
    const leaderboardId = formattedLeaderboards.leaderBoards[leaderboardKey].leaderBoardID;
    console.log(`Found leaderboard ID: ${leaderboardId}`);
    
    // Get the top entries for this leaderboard
    console.log('Fetching leaderboard entries...');
    const entriesResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARD_ENTRIES_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID,
        leaderboardid: leaderboardId,
        datarequest: 'RequestGlobal',
        rangestart: 0,
        rangeend: limit - 1
      }
    });
    
    // Extract and format the entries
    let entries = [];
    
    if (entriesResponse.data.leaderboardEntryInformation && entriesResponse.data.leaderboardEntryInformation.leaderboardEntries) {
      entries = entriesResponse.data.leaderboardEntryInformation.leaderboardEntries;
    } else if (entriesResponse.data.response && entriesResponse.data.response.entries) {
      entries = entriesResponse.data.response.entries;
    } else {
      console.error('Unexpected response structure:', entriesResponse.data);
      return res.status(500).json({ error: 'Unexpected response structure from Steam API' });
    }
    
    console.log(`Found ${entries.length} entries`);
    
    const formattedEntries = entries.map(entry => ({
      steamId: entry.steamID || entry.steamid,
      score: entry.score,
      rank: entry.rank,
      details: entry.details || entry.ugcid || null
    }));
    
    // Return the formatted response
    const response = {
      leaderboard: leaderboardKey,
      leaderboardId: leaderboardId,
      totalEntries: formattedLeaderboards.leaderBoards[leaderboardKey].leaderBoardEntries,
      topEntries: formattedEntries
    };
    
    console.log('Sending response...');
    res.json(response);
  } catch (error) {
    console.error('Error in leaderboard route:', error.message);
    console.error('Full error stack:', error.stack);
    res.status(500).json({ 
      error: 'Failed to retrieve leaderboard data',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error', message: err.message });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start the server
app.listen(config.PORT, () => {
  console.log(`Server running on port ${config.PORT}`);
  console.log(`API endpoint: http://localhost:${config.PORT}/tryu/leaderboard/[leaderboard_key]`);
});
