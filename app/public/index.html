<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Try&uuml; Sora</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');
css
Copy code
    body, html {
        margin: 0;
        padding: 0;
        height: 100%;
        background-color: black;
        font-family: 'Press Start 2P', cursive;
    }

    .container {
        padding-top:35vh;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        background-color: black;        
    }

    .deadfly-games {
        font-size: 1.3rem;
        color: #1CFF00;
        position: relative;
        text-shadow: 0 0 25px rgba(28, 255, 0, 1);
    }

    .cursor {
        position: absolute;
        top: 0;
        right: -24px;
        animation: blink 1s infinite;
        content: '';
        width: 22px;
        height: 20px;
        background-color: #1CFF00;
        box-shadow: 0 0 25px rgba(28, 255, 0, 1);
    }

    @keyframes blink {
        0%, 49% {
            opacity: 1;
        }
        50%, 100% {
            opacity: 0;
        }
    }
    
    .fly {
        text-align: center;
        rotate: 15deg;
    }
    
            .steam-button {
            display: inline-block;
            background-color: #1b2838;
            color: #fff;
            text-align: center;
            text-decoration: none;
            font-family: Arial, sans-serif;
            font-size: 16px;
            font-weight: bold;
            padding: 10px 20px;
            border: 2px solid #c0c0c0;
            border-radius: 5px;
            transition: background-color 0.3s, transform 0.2s;
        }

        .steam-button:hover {
            background-color: #4c6e92;
            transform: scale(1.05);
        }

        .steam-button img {
            vertical-align: middle;
            margin-right: 10px;
            height: 20px;
        }
</style>
</head>
<body>
    <div class="container">
        <div class="deadfly-games">
            <span id="t1">Try&uuml;</span><span id="t2"> Sora</span><span id="t3"> </span><span id="t4">Space Shooter</span>
            <div class="cursor"></div><br /><br /><br /><br />
               <div id="fly" class="fly">""<br />
            \\(<span id="t5">xx</span>)//<br />
            ~~
            </div>
        </div>
    </div>
<br /><br /><br /><br /><br />
<div align="center">

    <a class="steam-button" href="https://store.steampowered.com/app/3391210/Try_Sora__Space_Shooter/" target="_blank" rel="noopener noreferrer">
        <img src="https://upload.wikimedia.org/wikipedia/commons/8/83/Steam_icon_logo.svg" alt="Steam Logo">
        Get it on Steam
    </a>

    <a class="steam-button" href="/tryu_leaderboard" target="_blank" rel="noopener noreferrer">
        🏆 Steam Leaderboard
    </a>

    </div>

<script type="text/javascript">
    var _l = ['t1','t2','t3','t4','t5']
    
    function flicker()
    {
        setTimeout(()=>{
            for(var i=0;i<_l.length;i++)
            {
                document.getElementById(_l[i]).style.opacity = ""+(Math.random()*0.2+0.8);
            }
            flicker();
        },Math.random()*200);
        
    }
    
    flicker();
    
</script>
</body>
</html>