// Add noise effect element
document.addEventListener('DOMContentLoaded', function() {
    const screen = document.querySelector('.screen');
    const noise = document.createElement('div');
    noise.className = 'noise';
    screen.appendChild(noise);

    // Fetch leaderboard data once on page load
    fetchLeaderboards();
});

// Format score with commas
function formatScore(score) {
    return score.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Show loading overlay
function showLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    loadingOverlay.classList.add('active');

    // Add some retro terminal text effects
    const loadingText = loadingOverlay.querySelector('.loading-text');
    const originalText = loadingText.innerHTML;

    // Simulate typing effect
    loadingText.innerHTML = '';
    const textWithoutCursor = 'LOADING';

    let i = 0;
    const typeInterval = setInterval(() => {
        if (i < textWithoutCursor.length) {
            loadingText.innerHTML = textWithoutCursor.substring(0, i + 1) + '<span class="blink-cursor">_</span>';
            i++;
        } else {
            clearInterval(typeInterval);
        }
    }, 100);
}

// Hide loading overlay
function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');

    // Add a small delay for effect
    setTimeout(() => {
        loadingOverlay.classList.remove('active');
    }, 500);
}

// Fetch leaderboard data from our API
async function fetchLeaderboards() {
    try {
        // Show loading overlay
        showLoading();

        // Add random delay for retro feel (longer to show the loading animation)
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 1500));

        // API base URL
        const API_BASE_URL = 'https://tryusora.deadfly.games';

        console.log('Fetching campaign high scores...');
        // Fetch campaign high scores
        const campaignResponse = await fetch(`${API_BASE_URL}/api/tryu/leaderboard/campaign_high_score`);
        console.log('Campaign response status:', campaignResponse.status);
        const campaignData = await campaignResponse.json();
        console.log('Campaign data:', campaignData);

        console.log('Fetching flow high scores...');
        // Fetch flow high scores
        const flowResponse = await fetch(`${API_BASE_URL}/api/tryu/leaderboard/flow_high_score`);
        console.log('Flow response status:', flowResponse.status);
        const flowData = await flowResponse.json();
        console.log('Flow data:', flowData);

        // Cache status is now displayed statically in the HTML
        // No need to update it dynamically

        // Hide loading overlay
        hideLoading();

        // Display the data
        displayLeaderboard('campaign-scores', campaignData);
        displayLeaderboard('flow-scores', flowData);

        // Add loading effect
        addLoadingEffect();

    } catch (error) {
        console.error('Error fetching leaderboard data:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);

        // Hide loading overlay
        hideLoading();

        // Display error message in retro style
        document.getElementById('campaign-scores').innerHTML =
            '<div class="score-item error">ERROR: CANNOT LOAD DATA</div>';
        document.getElementById('flow-scores').innerHTML =
            '<div class="score-item error">ERROR: CANNOT LOAD DATA</div>';

        // Show error in a retro way
        addLoadingEffect();

        // Flash the screen red for error
        const screen = document.querySelector('.screen');
        screen.style.backgroundColor = '#150000';

        // Update the header text
        const header = document.querySelector('.header .blink');
        header.textContent = 'ERROR: CONNECTION FAILED';

        setTimeout(() => {
            screen.style.backgroundColor = '#001500';
        }, 200);
    }
}

// Display leaderboard data
function displayLeaderboard(elementId, data) {
    const scoresElement = document.getElementById(elementId);
    scoresElement.innerHTML = '';

    // Check if we have valid data
    if (!data || !data.topEntries || data.topEntries.length === 0) {
        scoresElement.innerHTML = '<div class="score-item">NO DATA AVAILABLE</div>';
        return;
    }

    // Create and append score items with a delay for retro effect
    // Ensure we display exactly 10 entries
    const entriesToShow = data.topEntries.slice(0, 10);
    entriesToShow.forEach((entry, index) => {
        setTimeout(() => {
            const scoreItem = document.createElement('div');
            scoreItem.className = 'score-item';
            // Set custom property for staggered animation
            scoreItem.style.setProperty('--item-index', index);

            const rank = document.createElement('span');
            rank.className = 'rank';
            rank.textContent = `${entry.rank}.`;

            const name = document.createElement('span');
            name.className = 'name';
            name.textContent = entry.playerName || 'UNKNOWN';

            const score = document.createElement('span');
            score.className = 'score';
            score.textContent = formatScore(entry.score);

            scoreItem.appendChild(rank);
            scoreItem.appendChild(name);
            scoreItem.appendChild(score);

            scoresElement.appendChild(scoreItem);

            // Add CRT scan effect
            scoreItem.style.opacity = '0';
            setTimeout(() => {
                scoreItem.style.transition = 'opacity 0.1s';
                scoreItem.style.opacity = '1';
            }, 50);

        }, index * 150); // Stagger the appearance of each item
    });
}

// Add retro loading effect
function addLoadingEffect() {
    const screen = document.querySelector('.screen');

    // Create loading glitch effect
    const glitch = document.createElement('div');
    glitch.style.position = 'absolute';
    glitch.style.top = '0';
    glitch.style.left = '0';
    glitch.style.width = '100%';
    glitch.style.height = '100%';
    glitch.style.backgroundColor = 'rgba(0, 255, 0, 0.05)';
    glitch.style.zIndex = '5';
    glitch.style.pointerEvents = 'none';

    screen.appendChild(glitch);

    // Remove after a short time
    setTimeout(() => {
        glitch.remove();
    }, 300);
}

// Cache status is now displayed statically in the HTML

// Add random glitches occasionally
setInterval(() => {
    if (Math.random() > 0.7) {
        addLoadingEffect();
    }
}, 5000);


