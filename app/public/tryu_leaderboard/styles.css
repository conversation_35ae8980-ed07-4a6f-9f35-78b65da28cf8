/* Import a monospace font that resembles C64 font */
@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #000;
    font-family: 'VT323', monospace;
    min-height: 100vh;
    display: block; /* Change from flex to block */
    padding: 10px;
    overflow-y: auto;
}

/* CRT screen effect */
.crt {
    width: 95vw;
    height: auto;
    min-height: auto; /* Remove min-height constraint */
    max-width: 1200px;
    background-color: #000;
    border-radius: 20px;
    padding: 20px;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5),
                inset 0 0 10px rgba(0, 255, 0, 0.3);
    overflow: visible; /* Allow content to determine height */
    margin: 20px auto; /* Center horizontally with some vertical spacing */
}

.crt::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        rgba(18, 16, 16, 0) 50%,
        rgba(0, 0, 0, 0.25) 50%
    );
    background-size: 100% 4px;
    z-index: 2;
    pointer-events: none;
    animation: scanline 0.1s linear infinite;
}

.crt::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        ellipse at center,
        rgba(0, 40, 0, 0.2) 0%,
        rgba(0, 40, 0, 0.4) 90%,
        rgba(0, 40, 0, 0.9) 100%
    );
    z-index: 1;
    pointer-events: none;
}

/* Screen content */
.screen {
    width: 100%;
    height: auto;
    min-height: auto; /* Remove min-height constraint */
    background-color: #001500;
    color: #00ff00;
    padding: 20px;
    position: relative;
    z-index: 3;
    display: flex;
    flex-direction: column;
    overflow: visible; /* Allow content to determine height */
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #00ff00;
    padding-bottom: 10px;
}

.header h1 {
    font-size: 3rem;
    letter-spacing: 2px;
    text-shadow: 0 0 5px #00ff00;
}

/* Content area with two columns */
.content {
    display: flex;
    gap: 20px;
}

.column {
    flex: 1;
    border: 2px solid #00ff00;
    padding: 15px;
    position: relative;
    overflow: visible; /* Allow content to determine height */
}

.column h2 {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.8rem;
    border-bottom: 1px solid #00ff00;
    padding-bottom: 5px;
}

/* Scores list */
.scores {
    font-size: 1.5rem;
    line-height: 1.6;
}

.score-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    animation: subtleFlicker 3s infinite;
    animation-delay: calc(var(--item-index, 0) * 0.2s);
}

@keyframes subtleFlicker {
    0%, 100% { opacity: 1; }
    95% { opacity: 1; }
    96% { opacity: 0.8; }
    97% { opacity: 1; }
    98% { opacity: 0.9; }
    99% { opacity: 1; }
}

.rank {
    margin-right: 10px;
}

.name {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.score {
    text-align: right;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 20px;
    border-top: 2px solid #00ff00;
    padding-top: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.cache-status {
    font-size: 0.8em;
    opacity: 0.7;
    color: #00cc00;
    font-style: italic;
}

/* Blinking cursor effect */
.blink {
    animation: blink 1s step-end infinite;
}

/* Retro text with flickering effect */
.retro-text {
    animation: glowAnim 4s infinite;
}

@keyframes glowAnim {
    0% { text-shadow: 0 0 0px #00ff00; }
    10% { text-shadow: 0 0 10px #00ff00; }
    40% { text-shadow: 0 0 3px #00ff00; }
    41% { text-shadow: 0 0 10px #00ff00; }
    42% { text-shadow: 0 0 3px #00ff00; }
    60% { text-shadow: 0 0 15px #00ff00; }
    80% { text-shadow: 0 0 4px #00ff00; }
    100% { text-shadow: 0 0 10px #00ff00; }
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 21, 0, 0.9);
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    padding: 20px;
    border: 2px solid #00ff00;
    background-color: #001500;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
    max-width: 80%;
}

.loading-text {
    font-size: 3rem;
    margin-bottom: 20px;
    letter-spacing: 3px;
    text-shadow: 0 0 5px #00ff00;
}

.blink-cursor {
    animation: blink 0.7s step-end infinite;
}

.loading-progress {
    height: 10px;
    width: 100%;
    background-color: #003300;
    position: relative;
    overflow: hidden;
}

.loading-progress::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: #00ff00;
    animation: progress 2s linear infinite;
}

@keyframes progress {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Noise effect */
.noise {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAABN0lEQVRoQ+2Y0Q3CMAxE2wkYgREYgREYgREYgREYgREYgQ0YAUZgBEZgBDpKpKJCVZPGTuoq8le/5OzLNbZbt+g8dM6jEJAWlVQiEolkIDAHaJoGd4uGRCTykwCaxnmkRZBIJJLBQJqmQdM4j7QIEolEMhhI0zRoGudhLRJKeAHkUcLzSCTiCQTXRSIRJBBcF4lEkEBwXSQSQQLBdZFIBAkE10UiESQQXBeJRJBAcF0kEkECwXWRSAQJBNdFIhEkEFwXiUSQQHBdJBJBAsF1kUgECQTXRSIRJBBcF4lEkEBwXSQSQQLBdZFIBAkE10UiESQQXBeJRJBAcF0kEkECwXWRSAQJBNdFIhEkEFyXbCLz+WJQVdVhPB4dq6o6W2vPeHfBlfnXIZvIv0L+9XkC0qKqSiQSyUBgDvAGC/9iE+nCGvIAAAAASUVORK5CYII=');
    opacity: 0.05;
    z-index: 4;
    pointer-events: none;
    animation: noise 0.2s infinite;
}

/* Animations */
@keyframes scanline {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 0 4px;
    }
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

@keyframes noise {
    0%, 100% {
        background-position: 0 0;
    }
    10% {
        background-position: -5% -10%;
    }
    20% {
        background-position: -15% 5%;
    }
    30% {
        background-position: 7% -25%;
    }
    40% {
        background-position: 20% 25%;
    }
    50% {
        background-position: -25% 10%;
    }
    60% {
        background-position: 15% 5%;
    }
    70% {
        background-position: 0% 15%;
    }
    80% {
        background-position: 25% 35%;
    }
    90% {
        background-position: -10% 10%;
    }
}

/* Flicker effect */
.screen {
    animation: flicker 0.3s infinite alternate;
}

@keyframes flicker {
    0%, 100% {
        opacity: 0.98;
    }
    92% {
        opacity: 0.95;
    }
    96% {
        opacity: 0.9;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    body {
        padding: 5px;
    }

    .crt {
        width: 100%;
        margin: 10px 0;
    }

    .content {
        flex-direction: column;
        gap: 10px;
    }

    .column {
        height: auto; /* Allow height to grow with content */
        min-height: auto; /* Remove min-height */
        max-height: none; /* Remove max-height */
    }

    .header h1 {
        font-size: 2rem;
    }

    .column h2 {
        font-size: 1.5rem;
    }

    .scores {
        font-size: 1.2rem;
    }
}

.steam-button {
    display: inline-block;
    background-color: #1b2838;
    color: #fff;
    text-align: center;
    text-decoration: none;
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    padding: 2px 5px;
    border: 2px solid #c0c0c0;
    border-radius: 5px;
    transition: background-color 0.3s, transform 0.2s;
}

.steam-button:hover {
    background-color: #4c6e92;
    transform: scale(1.05);
}

.steam-button img {
    vertical-align: middle;
    margin-right: 10px;
    height: 20px;
}
