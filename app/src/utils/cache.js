const fs = require('fs').promises;
const path = require('path');

// Create cache directory if it doesn't exist
const cacheDir = path.join(__dirname, '../../cache');
console.log('Cache directory path:', cacheDir);

const ensureCacheDir = async () => {
  try {
    await fs.mkdir(cacheDir, { recursive: true });
    console.log('Cache directory created or already exists at:', cacheDir);
  } catch (error) {
    console.error('Error creating cache directory:', error);
  }
};

// Initialize cache directory
ensureCacheDir();

/**
 * Cache utility for storing and retrieving API responses
 */
class CacheManager {
  /**
   * Constructor
   * @param {number} cacheDurationMinutes - Cache duration in minutes
   */
  constructor(cacheDurationMinutes = 10) {
    this.cacheDurationMs = cacheDurationMinutes * 60 * 1000;
  }

  /**
   * Get cache file path for a key
   * @param {string} key - Cache key
   * @returns {string} Cache file path
   */
  getCacheFilePath(key) {
    return path.join(cacheDir, `${key}.json`);
  }

  /**
   * Save data to cache
   * @param {string} key - Cache key
   * @param {object} data - Data to cache
   * @returns {Promise<void>}
   */
  async set(key, data) {
    try {
      const cacheData = {
        timestamp: Date.now(),
        data
      };

      const filePath = this.getCacheFilePath(key);
      console.log(`Saving cache to file: ${filePath}`);

      await fs.writeFile(
        filePath,
        JSON.stringify(cacheData),
        'utf8'
      );

      console.log(`Cache saved for key: ${key}`);
    } catch (error) {
      console.error(`Error saving cache for key ${key}:`, error);
      console.error(error.stack);
    }
  }

  /**
   * Get data from cache
   * @param {string} key - Cache key
   * @returns {Promise<object|null>} Cached data or null if not found or expired
   */
  async get(key) {
    try {
      const filePath = this.getCacheFilePath(key);

      // Check if cache file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        // File doesn't exist
        return null;
      }

      // Read cache file
      const cacheContent = await fs.readFile(filePath, 'utf8');
      const cacheData = JSON.parse(cacheContent);

      // Check if cache is expired
      const now = Date.now();
      if (now - cacheData.timestamp > this.cacheDurationMs) {
        console.log(`Cache expired for key: ${key}`);
        return null;
      }

      console.log(`Cache hit for key: ${key}`);
      return cacheData.data;
    } catch (error) {
      console.error(`Error reading cache for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Clear cache for a specific key
   * @param {string} key - Cache key
   * @returns {Promise<void>}
   */
  async clear(key) {
    try {
      const filePath = this.getCacheFilePath(key);
      await fs.unlink(filePath);
      console.log(`Cache cleared for key: ${key}`);
    } catch (error) {
      // Ignore if file doesn't exist
      if (error.code !== 'ENOENT') {
        console.error(`Error clearing cache for key ${key}:`, error);
      }
    }
  }

  /**
   * Clear all cache
   * @returns {Promise<void>}
   */
  async clearAll() {
    try {
      const files = await fs.readdir(cacheDir);

      for (const file of files) {
        if (file.endsWith('.json')) {
          await fs.unlink(path.join(cacheDir, file));
        }
      }

      console.log('All cache cleared');
    } catch (error) {
      console.error('Error clearing all cache:', error);
    }
  }
}

module.exports = CacheManager;
