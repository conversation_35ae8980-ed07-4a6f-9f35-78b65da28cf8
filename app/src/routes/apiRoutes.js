const express = require('express');
const router = express.Router();

module.exports = function(cache, config) {
  // API info route
  router.get('/', (_req, res) => {
    res.json({
      message: 'Steam Leaderboard API',
      endpoints: {
        leaderboards: '/tryu/leaderboard/:leaderboardKey',
        leaderboardsWithLimit: '/tryu/leaderboard/:leaderboardKey?limit=20',
        cacheStatus: '/api/cache',
        clearCache: '/api/cache/clear (POST)'
      }
    });
  });

  // Cache status route
  router.get('/cache', (_req, res) => {
    const cacheInfo = {};
    
    // Get cache stats
    Object.keys(cache.data).forEach(key => {
      const item = cache.data[key];
      const expiresIn = Math.round((item.expiresAt - Date.now()) / 1000);
      const age = Math.round((Date.now() - item.timestamp) / 1000);
      
      cacheInfo[key] = {
        age: `${age} seconds`,
        expiresIn: `${expiresIn} seconds`,
        expired: expiresIn <= 0
      };
    });
    
    res.json({
      cacheEnabled: true,
      cacheDuration: `${config.CACHE_DURATION_MINUTES} minutes`,
      cacheItems: Object.keys(cache.data).length,
      cache: cacheInfo
    });
  });

  // Clear cache route
  router.post('/cache/clear', (_req, res) => {
    const itemCount = Object.keys(cache.data).length;
    cache.data = {};
    console.log(`Cache cleared: ${itemCount} items removed`);
    res.json({
      success: true,
      message: `Cache cleared: ${itemCount} items removed`
    });
  });

  return router;
};
