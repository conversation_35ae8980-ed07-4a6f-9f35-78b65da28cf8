require('dotenv').config();

module.exports = {
  // Server configuration
  PORT: process.env.PORT || 3000,

  // Steam API configuration
  STEAM_API_KEY: process.env.STEAM_API_KEY,
  STEAM_APP_ID: process.env.STEAM_APP_ID,

  // Leaderboard configuration
  LEADERBOARD_RESULTS_LIMIT: parseInt(process.env.LEADERBOARD_RESULTS_LIMIT) || 10,

  // Cache configuration
  CACHE_DURATION_MINUTES: parseInt(process.env.CACHE_DURATION_MINUTES) || 10,

  // API endpoints
  STEAM_API_BASE_URL: 'https://partner.steam-api.com',
  LEADERBOARDS_FOR_GAME_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardsForGame/v2/',
  LEADERBOARD_ENTRIES_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardEntries/v1/'
};
