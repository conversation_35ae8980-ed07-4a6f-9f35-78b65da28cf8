const express = require('express');
const axios = require('axios');
const config = require('./config');
const fs = require('fs').promises;
const path = require('path');

// Create cache directory if it doesn't exist
const cacheDir = path.join(__dirname, '../cache');

// Ensure cache directory exists
async function ensureCacheDir() {
  try {
    await fs.mkdir(cacheDir, { recursive: true });
    console.log('Cache directory created or verified at:', cacheDir);
  } catch (error) {
    console.error('Error creating cache directory:', error);
  }
}

// Call this function when the server starts
ensureCacheDir();

// File-based cache implementation
const cache = {
  // Set cache data with expiration
  async set(key, value) {
    try {
      const cacheData = {
        timestamp: Date.now(),
        expiresAt: Date.now() + (config.CACHE_DURATION_MINUTES * 60 * 1000),
        value
      };

      const filePath = path.join(cacheDir, `${key}.json`);
      await fs.writeFile(filePath, JSON.stringify(cacheData), 'utf8');

      console.log(`Cache saved for key: ${key}, expires in ${config.CACHE_DURATION_MINUTES} minutes`);
      console.log(`Cache file: ${filePath}`);
    } catch (error) {
      console.error(`Error saving cache for key ${key}:`, error);
    }
  },

  // Get cache data if not expired
  async get(key) {
    try {
      const filePath = path.join(cacheDir, `${key}.json`);

      // Check if cache file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        console.log(`Cache file not found for key: ${key}`);
        return null;
      }

      // Read and parse cache file
      const data = await fs.readFile(filePath, 'utf8');
      const cacheData = JSON.parse(data);

      // Check if cache is expired
      if (Date.now() > cacheData.expiresAt) {
        console.log(`Cache expired for key: ${key}`);
        // Delete expired cache file
        try {
          await fs.unlink(filePath);
          console.log(`Deleted expired cache file: ${filePath}`);
        } catch (unlinkError) {
          console.error(`Error deleting expired cache file: ${filePath}`, unlinkError);
        }
        return null;
      }

      const remainingTime = Math.round((cacheData.expiresAt - Date.now()) / 1000);
      console.log(`Cache hit for key: ${key}, expires in ${remainingTime} seconds`);

      // Add cache status to the response
      if (cacheData.value && typeof cacheData.value === 'object') {
        cacheData.value.cache_status = {
          is_cached: true,
          cache_expires_in: remainingTime
        };
      }

      return cacheData.value;
    } catch (error) {
      console.error(`Error reading cache for key ${key}:`, error);
      return null;
    }
  },

  // List all cache entries
  async list() {
    try {
      const files = await fs.readdir(cacheDir);
      const cacheFiles = files.filter(file => file.endsWith('.json'));

      const cacheEntries = {};

      for (const file of cacheFiles) {
        try {
          const filePath = path.join(cacheDir, file);
          const data = await fs.readFile(filePath, 'utf8');
          const cacheData = JSON.parse(data);

          const key = file.replace('.json', '');
          const expiresIn = Math.round((cacheData.expiresAt - Date.now()) / 1000);
          const age = Math.round((Date.now() - cacheData.timestamp) / 1000);

          cacheEntries[key] = {
            age: `${age} seconds`,
            expiresIn: `${expiresIn} seconds`,
            expired: expiresIn <= 0,
            filePath
          };
        } catch (error) {
          console.error(`Error reading cache file ${file}:`, error);
        }
      }

      return cacheEntries;
    } catch (error) {
      console.error('Error listing cache entries:', error);
      return {};
    }
  },

  // Clear all cache
  async clear() {
    try {
      const files = await fs.readdir(cacheDir);
      const cacheFiles = files.filter(file => file.endsWith('.json'));

      let count = 0;
      for (const file of cacheFiles) {
        try {
          await fs.unlink(path.join(cacheDir, file));
          count++;
        } catch (error) {
          console.error(`Error deleting cache file ${file}:`, error);
        }
      }

      console.log(`Cleared ${count} cache files`);
      return count;
    } catch (error) {
      console.error('Error clearing cache:', error);
      return 0;
    }
  }
};

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.static('public'));

// CORS middleware for frontend access
app.use((_req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// Log all requests
app.use((req, _res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

console.log('Starting server with configuration:');
console.log('PORT:', config.PORT);
console.log('STEAM_API_KEY:', config.STEAM_API_KEY ? 'Set (hidden)' : 'Not set');
console.log('STEAM_APP_ID:', config.STEAM_APP_ID || 'Not set');

// Check if required environment variables are set
if (!config.STEAM_API_KEY || config.STEAM_API_KEY === 'your_steam_api_key_here') {
  console.error('STEAM_API_KEY is not set properly in environment variables');
  console.error('Please update the .env file with your Steam API key');
  // For development purposes, we'll continue anyway with a warning
  console.warn('Continuing without a valid API key - API calls will fail');
}

if (!config.STEAM_APP_ID || config.STEAM_APP_ID === 'your_app_id_here') {
  console.error('STEAM_APP_ID is not set properly in environment variables');
  console.error('Please update the .env file with your Steam App ID');
  // For development purposes, we'll continue anyway with a warning
  console.warn('Continuing without a valid App ID - API calls will fail');
}

// Root route - serve the frontend
app.get('/', (_req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// API info route
app.get('/api', (_req, res) => {
  res.json({
    message: 'Steam Leaderboard API',
    endpoints: {
      leaderboards: '/api/tryu/leaderboard/:leaderboardKey',
      cacheStatus: '/api/cache',
      clearCache: '/api/cache/clear (POST)'
    },
    config: {
      leaderboardResultsLimit: config.LEADERBOARD_RESULTS_LIMIT,
      cacheDurationMinutes: config.CACHE_DURATION_MINUTES
    }
  });
});

// Cache status route
app.get('/api/cache', async (_req, res) => {
  try {
    const cacheEntries = await cache.list();

    res.json({
      cacheEnabled: true,
      cacheDuration: `${config.CACHE_DURATION_MINUTES} minutes`,
      cacheItems: Object.keys(cacheEntries).length,
      cache: cacheEntries
    });
  } catch (error) {
    console.error('Error getting cache status:', error);
    res.status(500).json({ error: 'Failed to get cache status' });
  }
});

// Clear cache route
app.post('/api/cache/clear', async (_req, res) => {
  try {
    const count = await cache.clear();

    res.json({
      success: true,
      message: `Cache cleared: ${count} items removed`
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({ error: 'Failed to clear cache' });
  }
});

// Leaderboard route
app.get('/api/tryu/leaderboard/:leaderboardKey', async (req, res) => {
  try {
    console.log(`Request received for leaderboard: ${req.params.leaderboardKey}`);

    // Get the leaderboard key from the URL
    const { leaderboardKey } = req.params;

    // Use the fixed limit from configuration
    const limit = config.LEADERBOARD_RESULTS_LIMIT;
    console.log(`Using configured limit: ${limit}`);

    // Create a cache key based on the leaderboard key
    const cacheKey = `leaderboard_${leaderboardKey}`;

    // Try to get data from cache first
    const cachedData = await cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached data for ${leaderboardKey}`);
      return res.json(cachedData);
    }

    console.log(`No cache found or cache expired for ${leaderboardKey}, fetching fresh data...`);

    // Get all leaderboards for the game
    console.log('Fetching leaderboards...');
    const leaderboardsResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARDS_FOR_GAME_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID
      }
    });

    console.log('Leaderboards API response status:', leaderboardsResponse.status);

    // Format the leaderboards data
    const leaderboards = leaderboardsResponse.data.response.leaderboards || [];
    console.log(`Found ${leaderboards.length} leaderboards:`, leaderboards.map(lb => lb.name).join(', '));

    // Find the requested leaderboard
    const requestedLeaderboard = leaderboards.find(lb => lb.name === leaderboardKey);

    // Check if the requested leaderboard exists
    if (!requestedLeaderboard) {
      console.log(`Leaderboard ${leaderboardKey} not found`);
      return res.status(404).json({
        error: 'Leaderboard not found',
        availableLeaderboards: leaderboards.map(lb => lb.name)
      });
    }

    // Get the leaderboard ID
    const leaderboardId = requestedLeaderboard.id;
    console.log(`Found leaderboard ID: ${leaderboardId}`);

    // Get the top entries for this leaderboard
    console.log('Fetching leaderboard entries...');
    const entriesResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARD_ENTRIES_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID,
        leaderboardid: leaderboardId,
        datarequest: 'RequestGlobal',
        rangestart: 0,
        rangeend: limit - 1
      }
    });

    console.log('Entries API response status:', entriesResponse.status);

    // Extract and format the entries
    let entries = [];

    if (entriesResponse.data.leaderboardEntryInformation && entriesResponse.data.leaderboardEntryInformation.leaderboardEntries) {
      entries = entriesResponse.data.leaderboardEntryInformation.leaderboardEntries;
      console.log('Using leaderboardEntryInformation.leaderboardEntries structure');
    } else if (entriesResponse.data.response && entriesResponse.data.response.entries) {
      entries = entriesResponse.data.response.entries;
      console.log('Using response.entries structure');
    } else {
      console.error('Unexpected response structure:', JSON.stringify(entriesResponse.data));
      return res.status(500).json({ error: 'Unexpected response structure from Steam API' });
    }

    console.log(`Found ${entries.length} entries`);

    // Extract Steam IDs for player profile lookup
    const steamIds = entries.map(entry => entry.steamID || entry.steamid);

    // Get player profiles from Steam API
    console.log('Fetching player profiles...');
    let playerProfiles = {};

    try {
      const profilesResponse = await axios.get('https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/', {
        params: {
          key: config.STEAM_API_KEY,
          steamids: steamIds.join(',')
        }
      });

      // Create a map of steamId -> player profile for easy lookup
      const players = profilesResponse.data.response.players || [];
      players.forEach(player => {
        playerProfiles[player.steamid] = player;
      });

      console.log(`Retrieved ${players.length} player profiles`);
    } catch (profileError) {
      console.error('Error fetching player profiles:', profileError.message);
      // Continue without player names if there's an error
    }

    // Format entries with player names
    const formattedEntries = entries.map(entry => {
      const steamId = entry.steamID || entry.steamid;
      const playerProfile = playerProfiles[steamId] || {};

      return {
        steamId: steamId,
        playerName: playerProfile.personaname || 'Unknown Player',
        score: entry.score,
        rank: entry.rank,
        details: entry.details || entry.ugcid || null,
        avatarUrl: playerProfile.avatar || null
      };
    });

    // Return the formatted response
    const response = {
      leaderboard: leaderboardKey,
      leaderboardId: leaderboardId,
      totalEntries: requestedLeaderboard.entries,
      topEntries: formattedEntries,
      cache_status: {
        is_cached: false,
        cache_expires_in: config.CACHE_DURATION_MINUTES * 60 // in seconds
      }
    };

    // Save the response to cache
    await cache.set(cacheKey, response);

    console.log('Sending response and saving to cache...');
    res.json(response);
  } catch (error) {
    console.error('Error in leaderboard route:', error.message);

    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data));
    } else {
      console.error('Full error stack:', error.stack);
    }

    res.status(500).json({
      error: 'Failed to retrieve leaderboard data',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, _req, res, _next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error', message: err.message });
});

// Start the server
const server = app.listen(config.PORT, () => {
  console.log(`Server running on port ${config.PORT}`);
  console.log(`API endpoint: http://localhost:${config.PORT}/api/tryu/leaderboard/[leaderboard_key]`);
  console.log(`Example: curl http://localhost:${config.PORT}/api/tryu/leaderboard/flow_high_score`);
  console.log(`Cache duration: ${config.CACHE_DURATION_MINUTES} minutes`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    console.log('HTTP server closed');
  });
});

// 404 handler - must be the last middleware
app.use((req, res) => {
  console.log(`404 - Not Found: ${req.method} ${req.url}`);
  res.status(404).json({ error: 'Endpoint not found' });
});

module.exports = server;
