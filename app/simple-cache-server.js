const express = require('express');
const axios = require('axios');
require('dotenv').config();

// Configuration
const config = {
  PORT: process.env.PORT || 3809,
  STEAM_API_KEY: process.env.STEAM_API_KEY,
  STEAM_APP_ID: process.env.STEAM_APP_ID,
  CACHE_DURATION_MINUTES: process.env.CACHE_DURATION_MINUTES || 10,
  DEFAULT_TOP_RESULTS: 10,
  STEAM_API_BASE_URL: 'https://partner.steam-api.com',
  LEADERBOARDS_FOR_GAME_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardsForGame/v2/',
  LEADERBOARD_ENTRIES_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardEntries/v1/'
};

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Log all requests with detailed information
app.use((req, _res, next) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const ip = req.ip || req.connection.remoteAddress;

  console.log(`[${timestamp}] ${method} ${url} from ${ip}`);

  // Log request details
  if (process.env.DEBUG) {
    console.log('Request Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Request Query:', JSON.stringify(req.query, null, 2));
    console.log('Request Params:', JSON.stringify(req.params, null, 2));
  }

  next();
});

// CORS middleware
app.use((_req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// Simple in-memory cache with enhanced logging
const cache = {
  data: {},

  // Set cache data with expiration
  set(key, value) {
    try {
      if (!key) {
        console.error('Cannot set cache with empty key');
        return;
      }

      if (value === undefined || value === null) {
        console.error('Cannot set cache with undefined or null value');
        return;
      }

      const expirationMs = config.CACHE_DURATION_MINUTES * 60 * 1000;
      const now = Date.now();
      const expiresAt = now + expirationMs;

      this.data[key] = {
        value,
        timestamp: now,
        expiresAt: expiresAt
      };

      const expirationDate = new Date(expiresAt).toISOString();
      console.log(`[CACHE] SET: Key "${key}" cached successfully`);
      console.log(`[CACHE] Expires at ${expirationDate} (in ${config.CACHE_DURATION_MINUTES} minutes)`);
      console.log(`[CACHE] Total items in cache: ${Object.keys(this.data).length}`);

      return true;
    } catch (error) {
      console.error(`[CACHE] ERROR setting cache for key "${key}":`, error);
      return false;
    }
  },

  // Get cache data if not expired
  get(key) {
    try {
      if (!key) {
        console.error('[CACHE] Cannot get cache with empty key');
        return null;
      }

      const item = this.data[key];
      if (!item) {
        console.log(`[CACHE] MISS: Key "${key}" not found in cache`);
        return null;
      }

      const now = Date.now();
      if (now > item.expiresAt) {
        console.log(`[CACHE] EXPIRED: Key "${key}" has expired, removing from cache`);
        delete this.data[key];
        return null;
      }

      const remainingMs = item.expiresAt - now;
      const remainingSecs = Math.round(remainingMs / 1000);
      const remainingMins = Math.round(remainingSecs / 60);

      console.log(`[CACHE] HIT: Key "${key}" found in cache`);
      console.log(`[CACHE] Expires in ${remainingMins} minutes (${remainingSecs} seconds)`);

      return item.value;
    } catch (error) {
      console.error(`[CACHE] ERROR getting cache for key "${key}":`, error);
      return null;
    }
  },

  // Get all cache entries with stats
  getStats() {
    const stats = {
      totalItems: Object.keys(this.data).length,
      items: {}
    };

    const now = Date.now();

    Object.keys(this.data).forEach(key => {
      const item = this.data[key];
      const ageMs = now - item.timestamp;
      const remainingMs = item.expiresAt - now;

      stats.items[key] = {
        age: `${Math.round(ageMs / 1000)} seconds`,
        expiresIn: `${Math.round(remainingMs / 1000)} seconds`,
        expired: remainingMs <= 0,
        size: JSON.stringify(item.value).length
      };
    });

    return stats;
  },

  // Clear all cache or specific key
  clear(key = null) {
    if (key) {
      if (this.data[key]) {
        delete this.data[key];
        console.log(`[CACHE] CLEARED: Key "${key}" removed from cache`);
        return 1;
      }
      return 0;
    } else {
      const count = Object.keys(this.data).length;
      this.data = {};
      console.log(`[CACHE] CLEARED: All ${count} items removed from cache`);
      return count;
    }
  }
};

// Root route
app.get('/', (_req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// API info route
app.get('/api', (_req, res) => {
  res.json({
    message: 'Steam Leaderboard API',
    version: '1.1.0',
    endpoints: {
      leaderboards: '/tryu/leaderboard/:leaderboardKey',
      leaderboardsWithLimit: '/tryu/leaderboard/:leaderboardKey?limit=20',
      cacheStatus: '/api/cache',
      clearAllCache: '/api/cache/clear (POST)',
      clearSpecificCache: '/api/cache/:key (DELETE)'
    },
    cacheConfig: {
      enabled: true,
      durationMinutes: config.CACHE_DURATION_MINUTES
    }
  });

  console.log('[API] API info requested');
});

// Cache status route
app.get('/api/cache', (_req, res) => {
  try {
    const stats = cache.getStats();

    res.json({
      cacheEnabled: true,
      cacheDuration: `${config.CACHE_DURATION_MINUTES} minutes`,
      cacheItems: stats.totalItems,
      cache: stats.items
    });

    console.log(`[API] Cache status requested - ${stats.totalItems} items in cache`);
  } catch (error) {
    console.error('[API] Error getting cache status:', error);
    res.status(500).json({ error: 'Failed to get cache status' });
  }
});

// Clear cache route
app.post('/api/cache/clear', (_req, res) => {
  try {
    const itemCount = cache.clear();

    res.json({
      success: true,
      message: `Cache cleared: ${itemCount} items removed`
    });

    console.log(`[API] Cache cleared - ${itemCount} items removed`);
  } catch (error) {
    console.error('[API] Error clearing cache:', error);
    res.status(500).json({ error: 'Failed to clear cache' });
  }
});

// Clear specific cache item route
app.delete('/api/cache/:key', (req, res) => {
  try {
    const key = req.params.key;
    const result = cache.clear(key);

    if (result > 0) {
      res.json({
        success: true,
        message: `Cache item "${key}" removed`
      });
    } else {
      res.status(404).json({
        success: false,
        message: `Cache item "${key}" not found`
      });
    }
  } catch (error) {
    console.error('[API] Error clearing specific cache item:', error);
    res.status(500).json({ error: 'Failed to clear cache item' });
  }
});

// Leaderboard route
app.get('/tryu/leaderboard/:leaderboardKey', async (req, res) => {
  try {
    console.log(`Request received for leaderboard: ${req.params.leaderboardKey}`);

    // Get the leaderboard key from the URL
    const { leaderboardKey } = req.params;

    // Get the limit from query params or use default
    const limit = parseInt(req.query.limit) || config.DEFAULT_TOP_RESULTS;
    console.log(`Using limit: ${limit}`);

    // Create a cache key based on the leaderboard key and limit
    const cacheKey = `leaderboard_${leaderboardKey}_${limit}`;

    // Try to get data from cache first
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`[API] Using cached data for leaderboard "${leaderboardKey}"`);
      return res.json(cachedData);
    }

    console.log(`[API] Cache miss for leaderboard "${leaderboardKey}", fetching fresh data...`);

    // Get all leaderboards for the game
    console.log('Fetching leaderboards...');
    const leaderboardsResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARDS_FOR_GAME_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID
      }
    });

    console.log('Leaderboards API response status:', leaderboardsResponse.status);

    // Format the leaderboards data
    const leaderboards = leaderboardsResponse.data.response.leaderboards || [];
    console.log(`Found ${leaderboards.length} leaderboards:`, leaderboards.map(lb => lb.name).join(', '));

    // Find the requested leaderboard
    const requestedLeaderboard = leaderboards.find(lb => lb.name === leaderboardKey);

    // Check if the requested leaderboard exists
    if (!requestedLeaderboard) {
      console.log(`Leaderboard ${leaderboardKey} not found`);
      return res.status(404).json({
        error: 'Leaderboard not found',
        availableLeaderboards: leaderboards.map(lb => lb.name)
      });
    }

    // Get the leaderboard ID
    const leaderboardId = requestedLeaderboard.id;
    console.log(`Found leaderboard ID: ${leaderboardId}`);

    // Get the top entries for this leaderboard
    console.log('Fetching leaderboard entries...');
    const entriesResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARD_ENTRIES_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID,
        leaderboardid: leaderboardId,
        datarequest: 'RequestGlobal',
        rangestart: 0,
        rangeend: limit - 1
      }
    });

    console.log('Entries API response status:', entriesResponse.status);

    // Extract and format the entries
    let entries = [];

    if (entriesResponse.data.leaderboardEntryInformation && entriesResponse.data.leaderboardEntryInformation.leaderboardEntries) {
      entries = entriesResponse.data.leaderboardEntryInformation.leaderboardEntries;
      console.log('Using leaderboardEntryInformation.leaderboardEntries structure');
    } else if (entriesResponse.data.response && entriesResponse.data.response.entries) {
      entries = entriesResponse.data.response.entries;
      console.log('Using response.entries structure');
    } else {
      console.error('Unexpected response structure:', JSON.stringify(entriesResponse.data));
      return res.status(500).json({ error: 'Unexpected response structure from Steam API' });
    }

    console.log(`Found ${entries.length} entries`);

    // Extract Steam IDs for player profile lookup
    const steamIds = entries.map(entry => entry.steamID || entry.steamid);

    // Get player profiles from Steam API
    console.log('Fetching player profiles...');
    let playerProfiles = {};

    try {
      const profilesResponse = await axios.get('https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/', {
        params: {
          key: config.STEAM_API_KEY,
          steamids: steamIds.join(',')
        }
      });

      // Create a map of steamId -> player profile for easy lookup
      const players = profilesResponse.data.response.players || [];
      players.forEach(player => {
        playerProfiles[player.steamid] = player;
      });

      console.log(`Retrieved ${players.length} player profiles`);
    } catch (profileError) {
      console.error('Error fetching player profiles:', profileError.message);
      // Continue without player names if there's an error
    }

    // Format entries with player names
    const formattedEntries = entries.map(entry => {
      const steamId = entry.steamID || entry.steamid;
      const playerProfile = playerProfiles[steamId] || {};

      return {
        steamId: steamId,
        playerName: playerProfile.personaname || 'Unknown Player',
        score: entry.score,
        rank: entry.rank,
        details: entry.details || entry.ugcid || null,
        avatarUrl: playerProfile.avatar || null
      };
    });

    // Create the formatted response
    const response = {
      leaderboard: leaderboardKey,
      leaderboardId: leaderboardId,
      totalEntries: requestedLeaderboard.entries,
      topEntries: formattedEntries
    };

    // Save the response to cache
    cache.set(cacheKey, response);

    console.log(`[API] Sending response and saving to cache for leaderboard "${leaderboardKey}"...`);
    res.json(response);
  } catch (error) {
    console.error('Error in leaderboard route:', error.message);

    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data));
    } else {
      console.error('Full error stack:', error.stack);
    }

    res.status(500).json({
      error: 'Failed to retrieve leaderboard data',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, _req, res, _next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error', message: err.message });
});

// 404 handler - must be the last middleware
app.use((req, res) => {
  console.log(`404 - Not Found: ${req.method} ${req.url}`);
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start the server
const server = app.listen(config.PORT, () => {
  const divider = '='.repeat(50);
  console.log(divider);
  console.log(`TRYU LEADERBOARD API SERVER`);
  console.log(divider);
  console.log(`• Server running on port: ${config.PORT}`);
  console.log(`• API info: http://localhost:${config.PORT}/api`);
  console.log(`• Leaderboard endpoint: http://localhost:${config.PORT}/tryu/leaderboard/[leaderboard_key]`);
  console.log(`• Example: curl http://localhost:${config.PORT}/tryu/leaderboard/flow_high_score`);
  console.log(`• Cache duration: ${config.CACHE_DURATION_MINUTES} minutes`);
  console.log(`• Cache status: http://localhost:${config.PORT}/api/cache`);
  console.log(divider);
  console.log(`Server started at: ${new Date().toISOString()}`);
  console.log(divider);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    console.log('HTTP server closed');
  });
});
