{"name": "tryu_leaderboard", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "frontend": "node frontend-server.js", "cache-server": "node cache-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "express": "^5.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}