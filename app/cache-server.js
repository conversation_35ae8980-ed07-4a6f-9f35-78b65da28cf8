const express = require('express');
const axios = require('axios');
require('dotenv').config();

// Configuration
const config = {
  PORT: process.env.PORT || 3809,
  STEAM_API_KEY: process.env.STEAM_API_KEY,
  STEAM_APP_ID: process.env.STEAM_APP_ID,
  CACHE_DURATION_MINUTES: parseInt(process.env.CACHE_DURATION_MINUTES) || 10,
  LEADERBOARD_RESULTS_LIMIT: parseInt(process.env.LEADERBOARD_RESULTS_LIMIT) || 10,
  STEAM_API_BASE_URL: 'https://partner.steam-api.com',
  LEADERBOARDS_FOR_GAME_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardsForGame/v2/',
  LEADERBOARD_ENTRIES_ENDPOINT: '/ISteamLeaderboards/GetLeaderboardEntries/v1/'
};

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.static('public'));

// CORS middleware
app.use((_req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// Simple in-memory cache
const cache = new Map();

// Cache middleware
const cacheMiddleware = (req, res, next) => {
  // Only cache GET requests
  if (req.method !== 'GET') {
    return next();
  }

  const cacheKey = req.originalUrl;
  const cachedResponse = cache.get(cacheKey);

  if (cachedResponse && cachedResponse.expiresAt > Date.now()) {
    const remainingTime = Math.round((cachedResponse.expiresAt - Date.now()) / 1000);
    console.log(`[CACHE HIT] ${cacheKey}, expires in ${remainingTime} seconds`);

    // Add cache status to the response
    if (cachedResponse.data && typeof cachedResponse.data === 'object') {
      cachedResponse.data.cache_status = {
        is_cached: true,
        cache_expires_in: remainingTime
      };
    }

    return res.json(cachedResponse.data);
  }

  // Store the original res.json method
  const originalJson = res.json;

  // Override res.json method to cache the response
  res.json = function(data) {
    // Store in cache
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + (config.CACHE_DURATION_MINUTES * 60 * 1000)
    });

    console.log(`[CACHE SET] ${cacheKey}`);

    // Call the original method
    return originalJson.call(this, data);
  };

  next();
};

// API info route
app.get('/api', (_req, res) => {
  res.json({
    message: 'Steam Leaderboard API',
    version: '1.1.0',
    endpoints: {
      leaderboards: '/api/tryu/leaderboard/:leaderboardKey',
      cacheStatus: '/api/cache',
      clearCache: '/api/cache/clear (POST)'
    },
    config: {
      leaderboardResultsLimit: config.LEADERBOARD_RESULTS_LIMIT,
      cacheDurationMinutes: config.CACHE_DURATION_MINUTES
    }
  });
});

// Cache status route
app.get('/api/cache', (_req, res) => {
  const cacheInfo = {};
  const now = Date.now();

  cache.forEach((value, key) => {
    const remainingMs = value.expiresAt - now;
    cacheInfo[key] = {
      age: `${Math.round((now - value.timestamp) / 1000)} seconds`,
      expiresIn: `${Math.round(remainingMs / 1000)} seconds`,
      expired: remainingMs <= 0
    };
  });

  res.json({
    cacheEnabled: true,
    cacheDuration: `${config.CACHE_DURATION_MINUTES} minutes`,
    cacheItems: cache.size,
    cache: cacheInfo
  });
});

// Clear cache route
app.post('/api/cache/clear', (_req, res) => {
  const size = cache.size;
  cache.clear();

  res.json({
    success: true,
    message: `Cache cleared: ${size} items removed`
  });
});

// Apply cache middleware to leaderboard routes
app.use('/api/tryu/leaderboard', cacheMiddleware);

// Leaderboard route
app.get('/api/tryu/leaderboard/:leaderboardKey', async (req, res) => {
  try {
    console.log(`Request received for leaderboard: ${req.params.leaderboardKey}`);

    // Get the leaderboard key from the URL
    const { leaderboardKey } = req.params;

    // Use the fixed limit from configuration
    const limit = config.LEADERBOARD_RESULTS_LIMIT;
    console.log(`Using configured limit: ${limit}`);

    // Get all leaderboards for the game
    console.log('Fetching leaderboards...');
    const leaderboardsResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARDS_FOR_GAME_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID
      }
    });

    console.log('Leaderboards API response status:', leaderboardsResponse.status);

    // Format the leaderboards data
    const leaderboards = leaderboardsResponse.data.response.leaderboards || [];
    console.log(`Found ${leaderboards.length} leaderboards:`, leaderboards.map(lb => lb.name).join(', '));

    // Find the requested leaderboard
    const requestedLeaderboard = leaderboards.find(lb => lb.name === leaderboardKey);

    // Check if the requested leaderboard exists
    if (!requestedLeaderboard) {
      console.log(`Leaderboard ${leaderboardKey} not found`);
      return res.status(404).json({
        error: 'Leaderboard not found',
        availableLeaderboards: leaderboards.map(lb => lb.name)
      });
    }

    // Get the leaderboard ID
    const leaderboardId = requestedLeaderboard.id;
    console.log(`Found leaderboard ID: ${leaderboardId}`);

    // Get the top entries for this leaderboard
    console.log('Fetching leaderboard entries...');
    const entriesResponse = await axios.get(`${config.STEAM_API_BASE_URL}${config.LEADERBOARD_ENTRIES_ENDPOINT}`, {
      params: {
        key: config.STEAM_API_KEY,
        appid: config.STEAM_APP_ID,
        leaderboardid: leaderboardId,
        datarequest: 'RequestGlobal',
        rangestart: 0,
        rangeend: limit - 1
      }
    });

    console.log('Entries API response status:', entriesResponse.status);

    // Extract and format the entries
    let entries = [];

    if (entriesResponse.data.leaderboardEntryInformation && entriesResponse.data.leaderboardEntryInformation.leaderboardEntries) {
      entries = entriesResponse.data.leaderboardEntryInformation.leaderboardEntries;
      console.log('Using leaderboardEntryInformation.leaderboardEntries structure');
    } else if (entriesResponse.data.response && entriesResponse.data.response.entries) {
      entries = entriesResponse.data.response.entries;
      console.log('Using response.entries structure');
    } else {
      console.error('Unexpected response structure:', JSON.stringify(entriesResponse.data));
      return res.status(500).json({ error: 'Unexpected response structure from Steam API' });
    }

    console.log(`Found ${entries.length} entries`);

    // Extract Steam IDs for player profile lookup
    const steamIds = entries.map(entry => entry.steamID || entry.steamid);

    // Get player profiles from Steam API
    console.log('Fetching player profiles...');
    let playerProfiles = {};

    try {
      const profilesResponse = await axios.get('https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/', {
        params: {
          key: config.STEAM_API_KEY,
          steamids: steamIds.join(',')
        }
      });

      // Create a map of steamId -> player profile for easy lookup
      const players = profilesResponse.data.response.players || [];
      players.forEach(player => {
        playerProfiles[player.steamid] = player;
      });

      console.log(`Retrieved ${players.length} player profiles`);
    } catch (profileError) {
      console.error('Error fetching player profiles:', profileError.message);
      // Continue without player names if there's an error
    }

    // Format entries with player names
    const formattedEntries = entries.map(entry => {
      const steamId = entry.steamID || entry.steamid;
      const playerProfile = playerProfiles[steamId] || {};

      return {
        steamId: steamId,
        playerName: playerProfile.personaname || 'Unknown Player',
        score: entry.score,
        rank: entry.rank,
        details: entry.details || entry.ugcid || null,
        avatarUrl: playerProfile.avatar || null
      };
    });

    // Create the formatted response
    const response = {
      leaderboard: leaderboardKey,
      leaderboardId: leaderboardId,
      totalEntries: requestedLeaderboard.entries,
      topEntries: formattedEntries,
      cache_status: {
        is_cached: false,
        cache_expires_in: config.CACHE_DURATION_MINUTES * 60 // in seconds
      }
    };

    console.log('Sending response...');
    res.json(response);
  } catch (error) {
    console.error('Error in leaderboard route:', error.message);

    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data));
    } else {
      console.error('Full error stack:', error.stack);
    }

    res.status(500).json({
      error: 'Failed to retrieve leaderboard data',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, _req, res, _next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error', message: err.message });
});

// 404 handler - must be the last middleware
app.use((req, res) => {
  console.log(`404 - Not Found: ${req.method} ${req.url}`);
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start the server
const server = app.listen(config.PORT, () => {
  const divider = '='.repeat(50);
  console.log(divider);
  console.log(`TRYU LEADERBOARD API SERVER`);
  console.log(divider);
  console.log(`• Server running on port: ${config.PORT}`);
  console.log(`• API info: http://localhost:${config.PORT}/api`);
  console.log(`• Leaderboard endpoint: http://localhost:${config.PORT}/api/tryu/leaderboard/[leaderboard_key]`);
  console.log(`• Example: curl http://localhost:${config.PORT}/api/tryu/leaderboard/flow_high_score`);
  console.log(`• Cache duration: ${config.CACHE_DURATION_MINUTES} minutes`);
  console.log(`• Cache status: http://localhost:${config.PORT}/api/cache`);
  console.log(divider);
  console.log(`Server started at: ${new Date().toISOString()}`);
  console.log(divider);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    console.log('HTTP server closed');
  });
});
