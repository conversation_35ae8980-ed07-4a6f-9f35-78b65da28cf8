# Steam Leaderboard API

A Node.js application that fetches and serves Steam leaderboard data through a RESTful API.

## Features

- Fetches leaderboard data from Steam Web API
- Provides a simple REST API to access leaderboard data
- Configurable number of top entries to return
- Caching mechanism to reduce API calls and improve performance
- Configurable cache duration (default: 10 minutes)
- Enhanced cache server with cache status and management endpoints

## Prerequisites

- Node.js (v14 or higher)
- A Steam Web API key
- A Steam App ID

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Copy `.env.example` to `.env` and fill in your Steam API key and App ID:
   ```
   cp .env.example .env
   ```
4. Edit the `.env` file with your Steam API credentials

## Configuration

Edit the `.env` file to configure the application:

```
# Steam API Configuration
STEAM_API_KEY=your_steam_api_key_here
STEAM_APP_ID=your_app_id_here
PORT=3000

# Cache Configuration
CACHE_DURATION_MINUTES=10

# Leaderboard Configuration
LEADERBOARD_RESULTS_LIMIT=10
```

## Usage

### Start the API server

```
npm start
```

For development with auto-reload:

```
npm run dev
```

### Start the API server with enhanced caching

```
npm run cache-server
```

### Start the Frontend server

```
npm run frontend
```

Then open your browser at http://localhost:3810 to view the retro-styled leaderboard.

### API Endpoints

#### Get leaderboard data

```
GET /api/tryu/leaderboard/:leaderboardKey
```

Parameters:
- `leaderboardKey`: The name of the leaderboard to fetch

Example:
```
GET /api/tryu/leaderboard/campaign_high_score
```

The number of results is configured via the `LEADERBOARD_RESULTS_LIMIT` environment variable (default: 10).

#### Cache Management (Enhanced Cache Server)

Get cache status:
```
GET /api/cache
```

Clear all cache:
```
POST /api/cache/clear
```

Clear specific cache item:
```
DELETE /api/cache/:key
```

Response:
```json
{
  "leaderboard": "campaign_high_score",
  "leaderboardId": 15374663,
  "totalEntries": 26,
  "topEntries": [
    {
      "steamId": "76561198012345678",
      "playerName": "PlayerUsername",
      "score": 1000,
      "rank": 1,
      "details": null,
      "avatarUrl": "https://avatars.steamstatic.com/abc123.jpg"
    },
    ...
  ]
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200 OK`: Request successful
- `404 Not Found`: Leaderboard not found
- `500 Internal Server Error`: Server error

## License

ISC
