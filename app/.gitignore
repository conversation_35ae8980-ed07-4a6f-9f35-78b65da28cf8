# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache files
cache/
.cache/
.npm/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Build directories
dist/
build/
out/

# Temporary files
tmp/
temp/

# Debug files
.node_repl_history
.nyc_output/
coverage/

# Project specific
cache/
.env
.env.local
.env.development
.env.production
# Keep .env.example for reference
!.env.example
