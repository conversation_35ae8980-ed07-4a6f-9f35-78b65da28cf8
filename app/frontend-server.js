const express = require('express');
const path = require('path');
const app = express();
const PORT = 3810; // Use a different port than our API server

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Serve index.html for root route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`Frontend server running on port ${PORT}`);
  console.log(`Open http://localhost:${PORT} in your browser to view the leaderboard`);
});
